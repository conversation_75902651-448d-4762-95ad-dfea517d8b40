#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全局统一的单位转换工具模块
提供标准的cm转px转换方法，支持配置化的PPI设置

特性：
1. 统一的cm转px转换方法
2. 支持配置化的PPI设置
3. 高精度转换计算
4. 缓存机制优化性能
5. 遵循DRY、KISS、SOLID、YAGNI原则
"""

import logging
from typing import Union, Optional, Dict, Any
from functools import lru_cache

# 配置日志
log = logging.getLogger(__name__)

# 常量定义
CM_TO_INCH_RATIO = 0.393701  # 1厘米 = 0.393701英寸
DEFAULT_PPI = 72  # 默认PPI值
MIN_PPI = 1  # 最小PPI值
MAX_PPI = 3000  # 最大PPI值

class UnitConverter:
    """统一的单位转换器类"""
    
    def __init__(self, ppi: int = DEFAULT_PPI):
        """
        初始化单位转换器
        
        Args:
            ppi: 每英寸像素数，默认72
        """
        self.ppi = self._validate_ppi(ppi)
        self._conversion_cache = {}
    
    def _validate_ppi(self, ppi: int) -> int:
        """
        验证PPI值的有效性
        
        Args:
            ppi: 待验证的PPI值
            
        Returns:
            int: 有效的PPI值
        """
        if not isinstance(ppi, (int, float)):
            log.warning(f"无效的PPI类型: {type(ppi)}，使用默认值{DEFAULT_PPI}")
            return DEFAULT_PPI
        
        ppi = int(ppi)
        if ppi < MIN_PPI or ppi > MAX_PPI:
            log.warning(f"PPI值超出范围[{MIN_PPI}, {MAX_PPI}]: {ppi}，使用默认值{DEFAULT_PPI}")
            return DEFAULT_PPI
        
        return ppi
    
    def set_ppi(self, ppi: int) -> None:
        """
        设置PPI值
        
        Args:
            ppi: 新的PPI值
        """
        new_ppi = self._validate_ppi(ppi)
        if new_ppi != self.ppi:
            self.ppi = new_ppi
            self._conversion_cache.clear()  # 清除缓存
            log.info(f"PPI已更新为: {self.ppi}")
    
    def get_ppi(self) -> int:
        """
        获取当前PPI值
        
        Returns:
            int: 当前PPI值
        """
        return self.ppi
    
    @lru_cache(maxsize=1000)
    def cm_to_px(self, cm_value: Union[int, float]) -> int:
        """
        将厘米转换为像素
        
        Args:
            cm_value: 厘米值
            
        Returns:
            int: 像素值
        """
        if not isinstance(cm_value, (int, float)):
            log.error(f"无效的厘米值类型: {type(cm_value)}")
            return 0
        
        if cm_value < 0:
            log.warning(f"负数厘米值: {cm_value}，返回0")
            return 0
        
        # 计算：厘米 -> 英寸 -> 像素
        inches = cm_value * CM_TO_INCH_RATIO
        pixels = int(round(inches * self.ppi))
        
        return pixels
    
    @lru_cache(maxsize=1000)
    def px_to_cm(self, px_value: Union[int, float]) -> float:
        """
        将像素转换为厘米
        
        Args:
            px_value: 像素值
            
        Returns:
            float: 厘米值
        """
        if not isinstance(px_value, (int, float)):
            log.error(f"无效的像素值类型: {type(px_value)}")
            return 0.0
        
        if px_value < 0:
            log.warning(f"负数像素值: {px_value}，返回0.0")
            return 0.0
        
        # 计算：像素 -> 英寸 -> 厘米
        inches = px_value / self.ppi
        cm = inches / CM_TO_INCH_RATIO
        
        return round(cm, 3)  # 保留3位小数
    
    def convert_dimensions_cm_to_px(self, width_cm: Union[int, float], 
                                   height_cm: Union[int, float]) -> tuple[int, int]:
        """
        转换尺寸从厘米到像素
        
        Args:
            width_cm: 宽度（厘米）
            height_cm: 高度（厘米）
            
        Returns:
            tuple[int, int]: (宽度像素, 高度像素)
        """
        width_px = self.cm_to_px(width_cm)
        height_px = self.cm_to_px(height_cm)
        return width_px, height_px
    
    def convert_dimensions_px_to_cm(self, width_px: Union[int, float], 
                                   height_px: Union[int, float]) -> tuple[float, float]:
        """
        转换尺寸从像素到厘米
        
        Args:
            width_px: 宽度（像素）
            height_px: 高度（像素）
            
        Returns:
            tuple[float, float]: (宽度厘米, 高度厘米)
        """
        width_cm = self.px_to_cm(width_px)
        height_cm = self.px_to_cm(height_px)
        return width_cm, height_cm
    
    def get_conversion_info(self) -> Dict[str, Any]:
        """
        获取转换器信息
        
        Returns:
            Dict[str, Any]: 转换器信息
        """
        return {
            'ppi': self.ppi,
            'cm_to_inch_ratio': CM_TO_INCH_RATIO,
            'cache_size': len(self._conversion_cache),
            'conversion_factor': self.ppi * CM_TO_INCH_RATIO  # 1cm对应的像素数
        }
    
    def clear_cache(self) -> None:
        """清除转换缓存"""
        self._conversion_cache.clear()
        # 清除lru_cache
        self.cm_to_px.cache_clear()
        self.px_to_cm.cache_clear()
        log.debug("转换缓存已清除")


# 全局默认转换器实例
_default_converter = UnitConverter()

def get_default_converter() -> UnitConverter:
    """
    获取默认的单位转换器实例
    
    Returns:
        UnitConverter: 默认转换器实例
    """
    return _default_converter

def set_global_ppi(ppi: int) -> None:
    """
    设置全局PPI值
    
    Args:
        ppi: PPI值
    """
    _default_converter.set_ppi(ppi)
    log.info(f"全局PPI已设置为: {ppi}")

def get_global_ppi() -> int:
    """
    获取全局PPI值
    
    Returns:
        int: 全局PPI值
    """
    return _default_converter.get_ppi()

# 便捷函数
def cm_to_px(cm_value: Union[int, float], ppi: Optional[int] = None) -> int:
    """
    便捷函数：将厘米转换为像素
    
    Args:
        cm_value: 厘米值
        ppi: 可选的PPI值，如果不提供则使用全局PPI
        
    Returns:
        int: 像素值
    """
    if ppi is not None:
        converter = UnitConverter(ppi)
        return converter.cm_to_px(cm_value)
    else:
        return _default_converter.cm_to_px(cm_value)

def px_to_cm(px_value: Union[int, float], ppi: Optional[int] = None) -> float:
    """
    便捷函数：将像素转换为厘米
    
    Args:
        px_value: 像素值
        ppi: 可选的PPI值，如果不提供则使用全局PPI
        
    Returns:
        float: 厘米值
    """
    if ppi is not None:
        converter = UnitConverter(ppi)
        return converter.px_to_cm(px_value)
    else:
        return _default_converter.px_to_cm(px_value)

def convert_dimensions_cm_to_px(width_cm: Union[int, float], height_cm: Union[int, float], 
                               ppi: Optional[int] = None) -> tuple[int, int]:
    """
    便捷函数：转换尺寸从厘米到像素
    
    Args:
        width_cm: 宽度（厘米）
        height_cm: 高度（厘米）
        ppi: 可选的PPI值，如果不提供则使用全局PPI
        
    Returns:
        tuple[int, int]: (宽度像素, 高度像素)
    """
    if ppi is not None:
        converter = UnitConverter(ppi)
        return converter.convert_dimensions_cm_to_px(width_cm, height_cm)
    else:
        return _default_converter.convert_dimensions_cm_to_px(width_cm, height_cm)

def convert_dimensions_px_to_cm(width_px: Union[int, float], height_px: Union[int, float], 
                               ppi: Optional[int] = None) -> tuple[float, float]:
    """
    便捷函数：转换尺寸从像素到厘米
    
    Args:
        width_px: 宽度（像素）
        height_px: 高度（像素）
        ppi: 可选的PPI值，如果不提供则使用全局PPI
        
    Returns:
        tuple[float, float]: (宽度厘米, 高度厘米)
    """
    if ppi is not None:
        converter = UnitConverter(ppi)
        return converter.convert_dimensions_px_to_cm(width_px, height_px)
    else:
        return _default_converter.convert_dimensions_px_to_cm(width_px, height_px)

def initialize_from_config(config_manager) -> bool:
    """
    从配置管理器初始化全局转换器
    
    Args:
        config_manager: 配置管理器实例
        
    Returns:
        bool: 是否初始化成功
    """
    try:
        canvas_settings = config_manager.get_canvas_settings()
        ppi = canvas_settings.get('ppi', DEFAULT_PPI)
        set_global_ppi(ppi)
        log.info(f"从配置初始化单位转换器成功，PPI: {ppi}")
        return True
    except Exception as e:
        log.error(f"从配置初始化单位转换器失败: {str(e)}")
        return False
